# 全局配置功能实现任务

## 任务背景
用户需要增加一个全局配置功能，可以增加任意内容，使用JSON格式接收配置数据，例如：url=https://www.baidu.com, status=1 等等。

## 实现方案
- 每个应用(developer_app_id)只有一条全局配置记录
- 所有配置内容存储在JSON字段中
- 提供获取和编辑两个接口
- 参考现有SwitchConfig功能的实现模式

## 数据库设计
```sql
CREATE TABLE `global_config` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `developer_app_id` int unsigned NOT NULL DEFAULT '0' COMMENT '研发效能APP项目id',
  `config_data` json NOT NULL COMMENT '配置数据(JSON格式)',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_developer_app_id` (`developer_app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='全局配置表';
```

## 实现步骤
1. ✅ 创建数据库SQL文件
2. ✅ 创建GlobalConfig模型
3. ✅ 创建GlobalConfigValidation验证器
4. ✅ 创建GlobalConfigService服务类
5. ✅ 创建GlobalConfigController控制器
6. ✅ 添加路由配置
7. ✅ 创建功能文档

## API接口
- GET /global-config/getConfigInfo - 获取全局配置信息
- POST /global-config/edit - 编辑全局配置

## 配置数据示例
```json
{
  "url": "https://www.baidu.com",
  "status": 1,
  "timeout": 30,
  "custom_settings": {
    "feature_a": true,
    "feature_b": false
  }
}
```
