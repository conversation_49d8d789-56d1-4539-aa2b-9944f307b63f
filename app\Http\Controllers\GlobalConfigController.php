<?php

/**
 * 全局配置控制器
 * @desc 全局配置控制器
 * <AUTHOR> Assistant
 * @date 2024年
 */

namespace App\Http\Controllers;

use App\Http\Validation\GlobalConfigValidation;
use App\Services\Plugin\GlobalConfigService;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

class GlobalConfigController extends Controller
{
    /**
     * 全局配置信息
     * @doc
     * @throws ValidationException
     */
    public function getConfigInfo()
    {
        $params = GlobalConfigValidation::build()->developerAppId()->validate();
        try {
            $ret = (new GlobalConfigService($params))->getGlobalConfig();
            return $this->success($ret);
        } catch (Exception $e) {
            Log::error('配置页-获取全局配置信息接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->fail(Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 全局配置编辑
     * @doc
     * @throws ValidationException
     */
    public function edit()
    {
        $params = GlobalConfigValidation::build()->developerAppId()->configData()->validate();
        try {
            $ret = (new GlobalConfigService($params))->editGlobalConfig();
            if (empty($ret)) {
                return $this->fail(Response::HTTP_INTERNAL_SERVER_ERROR, "编辑失败");
            }
            return $this->success();
        } catch (Exception $e) {
            Log::error('配置页-全局配置编辑接口报错,原因:' . $e->getMessage() . ' in: ' . $e->getFile() . ' line: ' . $e->getLine());
            return $this->fail(Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
