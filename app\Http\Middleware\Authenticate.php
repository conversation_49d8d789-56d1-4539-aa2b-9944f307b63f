<?php

namespace App\Http\Middleware;

use App\Models\User;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class Authenticate
{
    /**
     * Get the path the user should be redirected to when they are not authenticated.
     *
     * @param Request $request
     * @param Closure $next
     * @param null $guard
     * @return mixed
     */
    public function handle(Request $request, Closure $next, $guard = null)
    {
        if (env('APP_ENV') == 'local') Auth::setUser(User::query()->find(1));
        if (!Auth::guard($guard)->check()) {
            return response()->json(['message' => '账号未登录', 'code' => 1002, 'data' => []], 401);
        }
        //执行逻辑
        return $next($request);
    }
}
