{"@timestamp":"2025-06-09T11:14:51.519+08:00","level_name":"ERROR","trace_id":"","biz_id":"/global-config/getConfigInfo","user_id":"","message":"Argument 1 passed to Illuminate\\Auth\\SessionGuard::setUser() must implement interface Illuminate\\Contracts\\Auth\\Authenticatable, null given, called in D:\\www\\external-plugin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php on line 340"}
{"@timestamp":"2025-06-09T11:17:25.867+08:00","level_name":"ERROR","trace_id":"","biz_id":"","user_id":"","message":"配置页-全局配置编辑接口报错,原因:Array to string conversion in: D:\\www\\external-plugin\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php line: 617"}
{"@timestamp":"2025-06-09T11:21:44.327+08:00","level_name":"ERROR","trace_id":"","biz_id":"","user_id":"","message":"配置页-全局配置编辑接口报错,原因:file_get_contents(/data/www/developer/monitor-config-change/webhook.conf): failed to open stream: No such file or directory in: D:\\www\\external-plugin\\app\\Services\\MonitorConfigChangeService.php line: 57"}
