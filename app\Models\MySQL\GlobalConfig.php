<?php

/**
 * 全局配置模型类
 * @desc 全局配置模型类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2025/06/09
 */

namespace App\Models\MySQL;

class GlobalConfig extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'global_config';

    /**
     * 主键字段
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 允许写入的字段
     *
     * @var array
     */
    protected $fillable = [
        'developer_app_id', // 研发效能APP项目id
        'config_data',      // 配置数据(JSON格式)
    ];

    /**
     * 字段类型转换
     *
     * @var array
     */
    protected $casts = [
        'config_data' => 'array', // 自动转换JSON字段为数组
    ];

    /**
     * 默认配置填充值
     * 当应用没有配置时返回的默认值
     */
    const FILL_DEFAULT_CONFIG = [
        'config_data' => [],
    ];
}
