<?php

/**
 * 全局配置服务类
 * @desc 全局配置服务类
 * <AUTHOR> Assistant
 * @date 2024年
 */

namespace App\Services\Plugin;

use App\Models\MySQL\GlobalConfig;
use App\Services\MonitorConfigChangeService;

class GlobalConfigService extends BaseService
{
    /**
     * 获取全局配置
     * @return array
     */
    public function getGlobalConfig()
    {
        $config = GlobalConfig::query()
            ->where('developer_app_id', $this->params['developer_app_id'])
            ->first();

        if (!empty($config)) {
            return $config->toArray();
        }

        // 没有配置时返回默认值
        return array_merge([
            'developer_app_id' => $this->params['developer_app_id'],
        ], GlobalConfig::FILL_DEFAULT_CONFIG);
    }

    /**
     * 编辑全局配置
     * @return int
     */
    public function editGlobalConfig()
    {
        $config = GlobalConfig::query()
            ->where('developer_app_id', $this->params['developer_app_id'])
            ->first();

        if (!empty($config)) {
            // 更新操作
            return $this->modifyConfig($config["id"]);
        } else {
            // 添加操作
            return $this->insertConfig();
        }
    }

    /**
     * 添加全局配置
     * @return int
     */
    public function insertConfig()
    {
        $insertParams = array_merge($this->params, [
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ]);

        $id = GlobalConfig::query()->insertGetId($insertParams);

        // 监控更改
        (new MonitorConfigChangeService(null, "外挂添加全局配置"))->monitor($insertParams);

        return $id;
    }

    /**
     * 编辑全局配置
     * @param $id
     * @return int
     */
    public function modifyConfig($id)
    {
        $updateParams = array_merge($this->params, [
            'updated_at' => date('Y-m-d H:i:s'),
        ]);

        // 查询数据
        $config = GlobalConfig::query()->where('id', $id)->first();
        // 判断是否找到
        if (empty($config)) {
            return null;
        }

        // 创建监控服务类
        $service = new MonitorConfigChangeService($config->toArray(), "外挂修改全局配置");

        // 更新数据
        $ret = $config->update($updateParams);

        // 监控
        $service->monitor($config->toArray());

        // 返回
        return $ret;
    }
}
