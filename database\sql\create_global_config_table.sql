-- 全局配置表创建SQL
-- 用途：存储应用的全局配置信息，支持任意JSON格式配置
-- 作者：AI Assistant
-- 创建时间：2024年

CREATE TABLE `global_config` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `developer_app_id` int unsigned NOT NULL DEFAULT '0' COMMENT '研发效能APP项目id',
  `config_data` json NOT NULL COMMENT '配置数据(JSON格式)',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_developer_app_id` (`developer_app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='全局配置表';

-- 插入示例数据（可选）
-- INSERT INTO `global_config` (`developer_app_id`, `config_data`, `created_at`, `updated_at`) 
-- VALUES (1, '{"url": "https://www.baidu.com", "status": 1, "timeout": 30}', NOW(), NOW());
