<?php

/**
 * 全局配置验证器
 * @desc 全局配置验证器
 * <AUTHOR> Assistant
 * @date 2024年
 */

namespace App\Http\Validation;

/**
 * @method static GlobalConfigValidation build()
 */
class GlobalConfigValidation extends BaseValidation
{
    /**
     * 效能后台APPID的校验
     *
     * @return $this
     */
    public function developerAppId(): GlobalConfigValidation
    {
        $this->rules['developer_app_id'] = 'required|integer|min:1';
        return $this;
    }

    /**
     * 配置数据校验
     * 验证JSON格式的配置数据
     *
     * @return $this
     */
    public function configData(): GlobalConfigValidation
    {
        $this->rules['config_data'] = 'required|array';
        return $this;
    }

    /**
     * 提示信息
     *
     * @return array
     */
    protected function messages(): array
    {
        return [
            'developer_app_id.required' => '应用ID不能为空',
            'developer_app_id.integer' => '应用ID必须是整数',
            'developer_app_id.min' => '应用ID必须大于0',
            'config_data.required' => '配置数据不能为空',
            'config_data.array' => '配置数据必须是有效的JSON格式',
        ];
    }
}
